substitutions:
    uv_ao_pin: GPIO35
    uv_update_interval: '5s'

sensor:
    - platform: adc
      id: uv_sensor_voltage
      name: 'UV Sensor Voltage'
      icon: 'mdi:weather-sunny-alert'
      unit_of_measurement: 'mV'
      device_class: voltage
      accuracy_decimals: 0
      entity_category: diagnostic
      update_interval: $uv_update_interval
      pin: $uv_ao_pin
      attenuation: 2.5db
      filters:
          - multiply: 1000.0

    - platform: copy
      source_id: uv_sensor_voltage
      id: uv_index
      name: 'UV Index'
      icon: 'mdi:white-balance-sunny'
      accuracy_decimals: 0
      unit_of_measurement: ''
      device_class: ''
      filters:
          - calibrate_linear:
                method: exact
                datapoints:
                    - 25.0 -> 0.0 # Below 50mV = UV Index 0
                    - 227.0 -> 1.0 # UV Index 1
                    - 318.0 -> 2.0 # UV Index 2
                    - 408.0 -> 3.0 # UV Index 3
                    - 503.0 -> 4.0 # UV Index 4
                    - 606.0 -> 5.0 # UV Index 5
                    - 696.0 -> 6.0 # UV Index 6
                    - 795.0 -> 7.0 # UV Index 7
                    - 881.0 -> 8.0 # UV Index 8
                    - 976.0 -> 9.0 # UV Index 9
                    - 1079.0 -> 10.0 # UV Index 10
                    - 1170.0 -> 11.0 # UV Index 11+
          - clamp:
                min_value: 0
                max_value: 11
          - round: 0
