substitutions:
    uv_ao_pin: GPIO35
    uv_update_interval: '5s'

sensor:
    - platform: adc
      id: uv_sensor_voltage
      name: 'UV Sensor Voltage'
      icon: 'mdi:weather-sunny-alert'
      unit_of_measurement: 'mV'
      device_class: voltage
      accuracy_decimals: 0
      entity_category: diagnostic
      update_interval: $uv_update_interval
      pin: $uv_ao_pin
      attenuation: 2.5db
      filters:
          - multiply: 1000.0

    - platform: template
      id: uv_index
      name: 'UV Index'
      icon: 'mdi:white-balance-sunny'
      accuracy_decimals: 0
      update_interval: $uv_update_interval
      lambda: |-
          float voltage = id(uv_sensor_voltage).state;

          if (voltage < 50) {
            return 0;
          } else if (voltage < 272.5) {  // Midpoint between 227 and 318
            return 1;
          } else if (voltage < 363) {    // Midpoint between 318 and 408
            return 2;
          } else if (voltage < 455.5) {  // Midpoint between 408 and 503
            return 3;
          } else if (voltage < 554.5) {  // Midpoint between 503 and 606
            return 4;
          } else if (voltage < 651) {    // Midpoint between 606 and 696
            return 5;
          } else if (voltage < 745.5) {  // Midpoint between 696 and 795
            return 6;
          } else if (voltage < 838) {    // Midpoint between 795 and 881
            return 7;
          } else if (voltage < 928.5) {  // Midpoint between 881 and 976
            return 8;
          } else if (voltage < 1027.5) { // Midpoint between 976 and 1079
            return 9;
          } else if (voltage < 1124.5) { // Midpoint between 1079 and 1170
            return 10;
          } else {
            return 11;
          }
